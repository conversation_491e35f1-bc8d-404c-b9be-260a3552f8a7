{% extends "base.html" %}

{% block title %}Configure Repping System - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link active" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-star text-warning me-2"></i>Repping System Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Automatic Role Assignment</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="trigger_word" class="form-label">Trigger Word</label>
                                <input type="text" class="form-control" id="trigger_word" name="trigger_word" 
                                       value="{{ config.trigger_word if config else '' }}" 
                                       placeholder="e.g., /leakin" required>
                                <div class="form-text">
                                    The word to look for in user custom statuses (case insensitive)
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="role_id" class="form-label">Role</label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <option value="">Select a role...</option>
                                </select>
                                <div class="form-text">
                                    The Discord role to assign to users with the trigger word in their status
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="channel_id" class="form-label">Notification Channel</label>
                                <select class="form-select" id="channel_id" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                </select>
                                <div class="form-text">
                                    The Discord channel where role assignment notifications will be sent
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if config and config.trigger_word %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Trigger Word:</strong>
                                <br><code>{{ config.trigger_word }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Role ID:</strong>
                                <br><code>{{ config.role_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Channel ID:</strong>
                                <br><code>{{ config.channel_id }}</code>
                            </div>
                        </div>
                        {% if config.log_channel_id %}
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ config.log_channel_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Ignored Users:</strong>
                                <br>{{ config.ignored_users|length if config.ignored_users else 0 }} users
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get IDs</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on a role or channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The repping system automatically:</p>
                        <ul class="mb-0">
                            <li>Monitors user custom statuses</li>
                            <li>Assigns roles when trigger word is found</li>
                            <li>Removes roles when trigger word is removed</li>
                            <li>Sends notifications to the configured channel</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Additional Settings</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">Use Discord commands for:</p>
                        <ul class="mb-0">
                            <li><code>/set-log-id</code> - Set detailed logging</li>
                            <li><code>/add-ignored-user</code> - Ignore specific users</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
    loadChannels();
});

function loadRoles() {
    fetch('/api/roles')
        .then(response => response.json())
        .then(roles => {
            const roleSelect = document.getElementById('role_id');
            roleSelect.innerHTML = '<option value="">Select a role...</option>';

            roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.name;

                // Select current role if editing
                {% if config and config.role_id %}
                if (role.id === '{{ config.role_id }}') {
                    option.selected = true;
                }
                {% endif %}

                roleSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading roles:', error);
            // Fallback to text input
            const roleSelect = document.getElementById('role_id');
            roleSelect.outerHTML = `
                <input type="text" class="form-control" id="role_id" name="role_id"
                       value="{{ config.role_id if config else '' }}"
                       placeholder="Role ID (e.g., 123456789012345678)" required>
            `;
        });
}

function loadChannels() {
    fetch('/api/channels')
        .then(response => response.json())
        .then(channels => {
            const channelSelect = document.getElementById('channel_id');
            channelSelect.innerHTML = '<option value="">Select a channel...</option>';

            // Filter to text channels only
            const textChannels = channels.filter(channel => channel.type === 'text');

            // Group by category
            const categorized = {};
            textChannels.forEach(channel => {
                const category = channel.category || 'No Category';
                if (!categorized[category]) {
                    categorized[category] = [];
                }
                categorized[category].push(channel);
            });

            // Add channels grouped by category
            Object.keys(categorized).sort().forEach(category => {
                if (category !== 'No Category') {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = category;
                    channelSelect.appendChild(optgroup);

                    categorized[category].forEach(channel => {
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = `# ${channel.name}`;

                        // Select current channel if editing
                        {% if config and config.channel_id %}
                        if (channel.id === '{{ config.channel_id }}') {
                            option.selected = true;
                        }
                        {% endif %}

                        optgroup.appendChild(option);
                    });
                }
            });

            // Add uncategorized channels
            if (categorized['No Category']) {
                categorized['No Category'].forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.id;
                    option.textContent = `# ${channel.name}`;

                    // Select current channel if editing
                    {% if config and config.channel_id %}
                    if (channel.id === '{{ config.channel_id }}') {
                        option.selected = true;
                    }
                    {% endif %}

                    channelSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading channels:', error);
            // Fallback to text input
            const channelSelect = document.getElementById('channel_id');
            channelSelect.outerHTML = `
                <input type="text" class="form-control" id="channel_id" name="channel_id"
                       value="{{ config.channel_id if config else '' }}"
                       placeholder="Channel ID (e.g., 123456789012345678)" required>
            `;
        });
}
</script>
{% endblock %}
