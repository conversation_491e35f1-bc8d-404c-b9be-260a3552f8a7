{% extends "base.html" %}

{% block title %}Stats - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link active" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-chart-bar text-success me-2"></i>Bot Activity</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <!-- Server Overview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-server me-2"></i>{{ server_info.name }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Server ID:</strong> {{ server_info.id }}</p>
                        <p><strong>Member Count:</strong> {{ server_info.member_count }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Bot Activity (7 days):</strong> {{ server_stats.bot_activity_7d }} events</p>
                        <p><strong>Last Updated:</strong> <span id="current-time"></span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Feature Statistics -->
        <div class="row">
            <!-- Repping System -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning">
                        <h6 class="mb-0"><i class="fas fa-star me-2"></i>Repping System</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-warning">{{ server_stats.repping_users }}</h3>
                            <p class="text-muted">Active Repping Users (30 days)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Temp Voice Channels -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info">
                        <h6 class="mb-0"><i class="fas fa-microphone me-2"></i>Temp Voice Channels</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-info">{{ server_stats.active_temp_channels }}</h4>
                                <small class="text-muted">Currently Active</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info">{{ server_stats.total_temp_channels_30d }}</h4>
                                <small class="text-muted">Created (30 days)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vent System -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-danger">
                        <h6 class="mb-0"><i class="fas fa-heart me-2"></i>Vent System</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-danger">{{ server_stats.vent_messages_30d }}</h3>
                            <p class="text-muted">Vent Messages (30 days)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sticky Messages -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success">
                        <h6 class="mb-0"><i class="fas fa-thumbtack me-2"></i>Sticky Messages</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-success">{{ server_stats.active_sticky_messages }}</h3>
                            <p class="text-muted">Active Sticky Messages</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gender Verification -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Gender Verification</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-primary">{{ server_stats.gender_tickets_30d }}</h3>
                            <p class="text-muted">Verification Tickets (30 days)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DM Support -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-secondary">
                        <h6 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>DM Support</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-light">{{ server_stats.dm_support_tickets_30d }}</h3>
                            <p class="text-muted">Support Tickets (30 days)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Breakdown -->
        {% if log_stats.category_stats %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Activity Breakdown (Last 7 Days)</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for category in log_stats.category_stats %}
                    <div class="col-md-4 mb-3">
                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                            <span class="text-capitalize">
                                {% if category._id == 'repping' %}
                                    <i class="fas fa-star text-warning me-1"></i>Repping
                                {% elif category._id == 'vent' %}
                                    <i class="fas fa-heart text-danger me-1"></i>Vent
                                {% elif category._id == 'tempvoice' %}
                                    <i class="fas fa-microphone text-info me-1"></i>Temp Voice
                                {% elif category._id == 'sticky' %}
                                    <i class="fas fa-thumbtack text-success me-1"></i>Sticky
                                {% elif category._id == 'dm_support' %}
                                    <i class="fas fa-ticket-alt text-primary me-1"></i>DM Support
                                {% elif category._id == 'gender_verification' %}
                                    <i class="fas fa-shield-alt text-secondary me-1"></i>Gender Verification
                                {% else %}
                                    <i class="fas fa-cog text-muted me-1"></i>{{ category._id|title }}
                                {% endif %}
                            </span>
                            <span class="badge bg-primary">{{ category.count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Set current time
    function updateTime() {
        const now = new Date();
        document.getElementById('current-time').textContent = now.toLocaleString();
    }

    // Update time on page load
    updateTime();

    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
</script>
{% endblock %}
