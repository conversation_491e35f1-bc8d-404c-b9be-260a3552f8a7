{% extends "base.html" %}

{% block title %}Dashboard - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link active" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Server Info -->
        <div class="server-info">
            <div class="d-flex align-items-center">
                {% if server_info.icon %}
                <img src="{{ server_info.icon }}" alt="{{ server_info.name }}" class="server-avatar me-3">
                {% else %}
                <div class="server-avatar me-3 d-flex align-items-center justify-content-center bg-secondary">
                    <i class="fas fa-server fa-2x"></i>
                </div>
                {% endif %}
                <div>
                    <h2 class="mb-1">{{ server_info.name }}</h2>
                    <p class="text-muted mb-2">
                        <i class="fas fa-users me-1"></i>{{ server_info.member_count }} members
                        <span class="ms-3">
                            <i class="fas fa-id-badge me-1"></i>{{ server_info.id }}
                        </span>
                    </p>
                    {% if is_configured %}
                    <span class="badge bg-success status-badge">
                        <i class="fas fa-check me-1"></i>Fully Configured
                    </span>
                    {% else %}
                    <span class="badge bg-warning status-badge">
                        <i class="fas fa-exclamation-triangle me-1"></i>Configuration Incomplete
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Configuration Status -->
        {% if not is_configured %}
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Configuration Required</h6>
            <p class="mb-2">Your server is missing some required configuration:</p>
            <ul class="mb-0">
                {% for field in missing_fields %}
                <li>{{ field.replace('_', ' ').title() }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <!-- Feature Cards -->
        <div class="row g-4">
            <!-- Repping System -->
            <div class="col-lg-6">
                <div class="card feature-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>Repping System
                        </h6>
                        {% if config and config.trigger_word %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if config and config.trigger_word %}
                        <p class="mb-2"><strong>Trigger Word:</strong> <code>{{ config.trigger_word }}</code></p>
                        <p class="mb-2"><strong>Role ID:</strong> {{ config.role_id }}</p>
                        <p class="mb-3"><strong>Channel ID:</strong> {{ config.channel_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Configure automatic role assignment based on user status.</p>
                        {% endif %}
                        <a href="{{ url_for('configure_repping') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Vent System -->
            <div class="col-lg-6">
                <div class="card feature-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-heart text-danger me-2"></i>Vent System
                        </h6>
                        {% if vent_settings %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if vent_settings %}
                        <p class="mb-3"><strong>Vent Channel ID:</strong> {{ vent_settings.vent_channel_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Allow users to send anonymous messages to a designated channel.</p>
                        {% endif %}
                        <a href="{{ url_for('configure_vent') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Temp Voice -->
            <div class="col-lg-6">
                <div class="card feature-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-microphone text-info me-2"></i>Temp Voice
                        </h6>
                        {% if tempvoice_settings %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if tempvoice_settings %}
                        <p class="mb-2"><strong>Interface Channel:</strong> {{ tempvoice_settings.interface_channel_id }}</p>
                        <p class="mb-3"><strong>Creator Channel:</strong> {{ tempvoice_settings.creator_channel_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Temporary voice channels with full user management.</p>
                        {% endif %}
                        <a href="{{ url_for('configure_tempvoice') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sticky Messages -->
            <div class="col-lg-6">
                <div class="card feature-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-thumbtack text-success me-2"></i>Sticky Messages
                        </h6>
                        {% if sticky_messages %}
                        <span class="badge bg-success">{{ sticky_messages|length }} Active</span>
                        {% else %}
                        <span class="badge bg-secondary">None Active</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if sticky_messages %}
                        <p class="mb-3">{{ sticky_messages|length }} sticky message(s) configured</p>
                        {% else %}
                        <p class="text-muted mb-3">Persistent messages that automatically repost.</p>
                        {% endif %}
                        <a href="{{ url_for('configure_sticky_messages') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- DM Support -->
            <div class="col-lg-6">
                <div class="card feature-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-ticket-alt text-primary me-2"></i>DM Support
                        </h6>
                        {% if dm_support_settings %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if dm_support_settings %}
                        <p class="mb-2"><strong>Category ID:</strong> {{ dm_support_settings.category_id }}</p>
                        <p class="mb-3"><strong>Support Role:</strong> {{ dm_support_settings.support_role_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">DM-based support ticket system with admin responses.</p>
                        {% endif %}
                        <a href="{{ url_for('configure_dm_support') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Gender Verification -->
            <div class="col-lg-6">
                <div class="card feature-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt text-secondary me-2"></i>Gender Verification
                        </h6>
                        {% if gender_verification_settings %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if gender_verification_settings %}
                        <p class="mb-2"><strong>Channel ID:</strong> {{ gender_verification_settings.channel_id }}</p>
                        <p class="mb-3"><strong>Category ID:</strong> {{ gender_verification_settings.category_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Manual verification system with ticket support.</p>
                        {% endif %}
                        <a href="{{ url_for('configure_gender_verification') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Dashboard functionality can be added here if needed
</script>
{% endblock %}
