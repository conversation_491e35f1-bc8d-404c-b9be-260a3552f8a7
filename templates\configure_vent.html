{% extends "base.html" %}

{% block title %}Configure Vent System - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link active" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-heart text-danger me-2"></i>Vent System Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        {% if not config or not config.log_channel_id %}
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Log Channel Required</h6>
            <p class="mb-0">
                The vent system requires a log channel to be configured first for moderation purposes. 
                Please configure the repping system first to set up logging.
            </p>
        </div>
        {% endif %}

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Anonymous Venting</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="vent_channel_id" class="form-label">Vent Channel</label>
                                <select class="form-select" id="vent_channel_id" name="vent_channel_id" required
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <option value="">Select a channel...</option>
                                </select>
                                <div class="form-text">
                                    The Discord channel where anonymous vent messages will be posted
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" 
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if vent_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Vent Channel ID:</strong>
                                <br><code>{{ vent_settings.vent_channel_id }}</code>
                            </div>
                            {% if config and config.log_channel_id %}
                            <div class="col-md-6">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ config.log_channel_id }}</code>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get Channel ID</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on the channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The vent system allows users to:</p>
                        <ul class="mb-3">
                            <li>Send anonymous messages using <code>/vent</code></li>
                            <li>Messages appear anonymous to other users</li>
                            <li>All messages are logged for moderation</li>
                            <li>Requires premium license validation</li>
                        </ul>
                        <p class="text-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Privacy Note:</strong> While messages appear anonymous to users, 
                            all vent messages are logged with user information for moderation purposes.
                        </p>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Moderation</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">All vent messages are logged to your configured log channel with:</p>
                        <ul class="mb-0">
                            <li>User ID and username</li>
                            <li>Full message content</li>
                            <li>Timestamp</li>
                            <li>Server information</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    {% if config and config.log_channel_id %}
    loadChannels();
    {% endif %}
});

function loadChannels() {
    fetch('/api/channels')
        .then(response => response.json())
        .then(channels => {
            const channelSelect = document.getElementById('vent_channel_id');
            channelSelect.innerHTML = '<option value="">Select a channel...</option>';

            // Filter to text channels only
            const textChannels = channels.filter(channel => channel.type === 'text');

            // Group by category
            const categorized = {};
            textChannels.forEach(channel => {
                const category = channel.category || 'No Category';
                if (!categorized[category]) {
                    categorized[category] = [];
                }
                categorized[category].push(channel);
            });

            // Add channels grouped by category
            Object.keys(categorized).sort().forEach(category => {
                if (category !== 'No Category') {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = category;
                    channelSelect.appendChild(optgroup);

                    categorized[category].forEach(channel => {
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = `# ${channel.name}`;

                        // Select current channel if editing
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        if (channel.id === '{{ vent_settings.vent_channel_id }}') {
                            option.selected = true;
                        }
                        {% endif %}

                        optgroup.appendChild(option);
                    });
                }
            });

            // Add uncategorized channels
            if (categorized['No Category']) {
                categorized['No Category'].forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.id;
                    option.textContent = `# ${channel.name}`;

                    // Select current channel if editing
                    {% if vent_settings and vent_settings.vent_channel_id %}
                    if (channel.id === '{{ vent_settings.vent_channel_id }}') {
                        option.selected = true;
                    }
                    {% endif %}

                    channelSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading channels:', error);
        });
}
</script>
{% endblock %}
