{% extends "base.html" %}

{% block title %}Settings - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link active" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-cog text-secondary me-2"></i>Server Settings</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <!-- Settings Overview Cards -->
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                        <h5>Log Channel</h5>
                        {% if config and config.log_channel_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-warning">Not Set</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-slash fa-3x text-secondary mb-3"></i>
                        <h5>Ignored Users</h5>
                        <span class="badge bg-info">{{ ignored_users|length }} users</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                        <h5>Monitored Users</h5>
                        <span class="badge bg-success">{{ server_info.member_count - ignored_users|length }} users</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Log Channel Configuration -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Discord Log Channel</h5>
                        {% if config and config.log_channel_id %}
                        <span class="badge bg-success">Active</span>
                        {% else %}
                        <span class="badge bg-warning">Required</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Important:</strong> The log channel receives moderation logs for role assignments, vent messages, and configuration changes. This is separate from the dashboard logs.
                        </div>

                        <form method="POST">
                            <input type="hidden" name="action" value="set_log_channel">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <label for="log_channel_id" class="form-label">Select Log Channel</label>
                                    <select class="form-select" id="log_channel_id" name="log_channel_id" required>
                                        <option value="">Choose a channel...</option>
                                    </select>
                                    <div class="form-text">
                                        This channel will receive Discord notifications for bot activities
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save me-2"></i>Update Channel
                                    </button>
                                </div>
                            </div>
                        </form>

                        {% if config and config.log_channel_id %}
                        <div class="mt-3 p-3 bg-success bg-opacity-10 border border-success rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <div>
                                        <strong>Log Channel Active</strong>
                                        <br><small class="text-muted">Channel ID: {{ config.log_channel_id }}</small>
                                    </div>
                                </div>
                                <div>
                                    <small class="text-muted">✅ Vent System Ready</small><br>
                                    <small class="text-muted">✅ Role Logging Active</small>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="mt-3 p-3 bg-warning bg-opacity-10 border border-warning rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                <div>
                                    <strong>Log Channel Required</strong>
                                    <br><small class="text-muted">Vent system and detailed logging require a log channel</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Ignored Users Management -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-user-slash me-2"></i>Automod Settings</h5>
                        <span class="badge bg-secondary">{{ ignored_users|length }} ignored</span>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Ignored users</strong> are excluded from the repping system. They won't have roles automatically assigned or removed based on their status.
                        </div>

                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="add_ignored_user">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <label for="user_id" class="form-label">Add User to Ignore List</label>
                                    <input type="text" class="form-control" id="user_id" name="user_id"
                                           placeholder="Enter Discord User ID (e.g., 123456789012345678)" required>
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        Right-click a user → Copy ID (Developer Mode must be enabled)
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-user-plus me-2"></i>Add to Ignore List
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Current Ignored Users -->
                        {% if ignored_users %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Ignored Users</h6>
                            <small class="text-muted">{{ ignored_users|length }} user(s) ignored</small>
                        </div>

                        <div class="row g-2">
                            {% for user_id in ignored_users %}
                            <div class="col-md-6">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <code class="text-info">{{ user_id }}</code>
                                                <br><small class="text-muted">User ID</small>
                                            </div>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="remove_ignored_user">
                                                <input type="hidden" name="user_id" value="{{ user_id }}">
                                                <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        onclick="return confirm('Remove this user from the ignore list?')"
                                                        title="Remove from ignore list">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Ignored Users</h6>
                            <p class="text-muted mb-0">All users are currently monitored by the repping system</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>About Log Channels</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The log channel receives:</p>
                        <ul class="mb-3">
                            <li>Role assignment notifications</li>
                            <li>Vent message moderation logs</li>
                            <li>Configuration changes</li>
                            <li>Error notifications</li>
                        </ul>
                        <p class="text-info mb-0">
                            <i class="fas fa-lightbulb me-1"></i>
                            <strong>Tip:</strong> Create a dedicated #bot-logs channel for better organization.
                        </p>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-slash me-2"></i>About Ignored Users</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Ignored users are:</p>
                        <ul class="mb-3">
                            <li>Excluded from the repping system</li>
                            <li>Won't get roles automatically assigned</li>
                            <li>Won't have roles removed</li>
                            <li>Useful for bots and special accounts</li>
                        </ul>
                        <p class="text-info mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Note:</strong> All bots are automatically ignored by the system.
                        </p>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Advanced Settings</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Additional configuration options:</p>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('configure_repping') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-star me-1"></i>Repping System
                            </a>
                            <a href="{{ url_for('configure_vent') }}" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-heart me-1"></i>Vent System
                            </a>
                            <a href="{{ url_for('configure_tempvoice') }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-microphone me-1"></i>Temp Voice
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-external-link-alt me-2"></i>Quick Links</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('logs') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-alt me-1"></i>View Activity Logs
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-tachometer-alt me-1"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadLogChannels();
});

function loadLogChannels() {
    fetch('/api/channels')
        .then(response => response.json())
        .then(channels => {
            const channelSelect = document.getElementById('log_channel_id');
            channelSelect.innerHTML = '<option value="">Select a channel...</option>';
            
            // Filter to text channels only
            const textChannels = channels.filter(channel => channel.type === 'text');
            
            // Group by category
            const categorized = {};
            textChannels.forEach(channel => {
                const category = channel.category || 'No Category';
                if (!categorized[category]) {
                    categorized[category] = [];
                }
                categorized[category].push(channel);
            });
            
            // Add channels grouped by category
            Object.keys(categorized).sort().forEach(category => {
                if (category !== 'No Category') {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = category;
                    channelSelect.appendChild(optgroup);
                    
                    categorized[category].forEach(channel => {
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = `# ${channel.name}`;
                        
                        // Select current channel if editing
                        {% if config and config.log_channel_id %}
                        if (channel.id === '{{ config.log_channel_id }}') {
                            option.selected = true;
                        }
                        {% endif %}
                        
                        optgroup.appendChild(option);
                    });
                }
            });
            
            // Add uncategorized channels
            if (categorized['No Category']) {
                categorized['No Category'].forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.id;
                    option.textContent = `# ${channel.name}`;
                    
                    // Select current channel if editing
                    {% if config and config.log_channel_id %}
                    if (channel.id === '{{ config.log_channel_id }}') {
                        option.selected = true;
                    }
                    {% endif %}
                    
                    channelSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading channels:', error);
        });
}
</script>
{% endblock %}
